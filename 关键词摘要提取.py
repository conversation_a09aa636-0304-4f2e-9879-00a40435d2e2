#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word(.docx / .doc) → 分段摘要 → 汇总关键词+摘要
· 支持多 API-Key 轮换与 429 冷却
· 自动清洗孤立代理字符，避免 UTF-8 编码异常
· 依赖：pip install python-docx textract tiktoken openai pyyaml
        ├─ .docx  → python-docx
        └─ .doc   → textract（内部走 antiword、catdoc、libreoffice 等）
"""

import os, json, time, re, random, logging, sys, subprocess
from pathlib import Path
import yaml, openai

# ─── 依赖：提取 Word 文本 ────────────────────────────────────────────────
try:
    import docx
except ImportError:
    print("❗ 缺少 python-docx，请先：pip install python-docx")
    sys.exit(1)
try:
    import textract  # 用于 .doc；若无法安装，可只处理 .docx
except ImportError:
    textract = None

# ─── 参数 ────────────────────────────────────────────────────────────────
CHUNK_TOKENS = 3000  # 单段≈3k token
BASE_SLEEP = 2  # 每段成功后最少停 2 s
MAX_RETRY_SEG = 20  # 单段最多换 key 重试

# ─── 日志 ────────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)-7s %(message)s",
    datefmt="%H:%M:%S",
    handlers=[logging.StreamHandler(),
              logging.FileHandler("run.log", "a", "utf-8")])
log = logging.getLogger(__name__)

# ─── 配置 ────────────────────────────────────────────────────────────────
conf = yaml.safe_load(Path("config.yaml").read_text(encoding="utf-8"))
model = conf["openai"]["model"]
temperature = conf["openai"].get("temperature", 0.2)
max_tokens = conf["openai"].get("max_tokens", 2000)
prompt_root = conf["prompt"]["extract"]  # “请将下列段落概括为 …” 之类
docs_dir = Path(conf["word_directory"])  # ← 改成 word_directory
out_dir = Path(conf["output_directory"]).resolve()
out_dir.mkdir(exist_ok=True, parents=True)

api_keys = [k.strip() for k in Path(conf["openai"]["api_keys_file"]).read_text().splitlines() if k.strip()]
if not api_keys:
    log.error("❌ API Key 文件为空");
    raise SystemExit
random.shuffle(api_keys)
client = openai.OpenAI(api_key=api_keys[0])

# ─── tokenizer ──────────────────────────────────────────────────────────
try:
    import tiktoken

    enc = tiktoken.encoding_for_model(model)


    def ntokens(tx):
        return len(enc.encode(tx))


    def splitter(tx, size=CHUNK_TOKENS):
        ids = enc.encode(tx)
        for i in range(0, len(ids), size):
            yield enc.decode(ids[i:i + size])
except Exception:
    log.warning("⚠️  无 tiktoken，用字符长度近似 token")


    def ntokens(tx):
        return int(len(tx) / 1.8)


    def splitter(tx, size=CHUNK_TOKENS):
        step = size * 2
        for i in range(0, len(tx), step):
            yield tx[i:i + step]

# ─── Unicode 清洗 ────────────────────────────────────────────────────────
_surrogates = re.compile(r'[\uD800-\uDFFF]')


def scrub(text: str) -> str:
    return _surrogates.sub('', text)


# ─── OpenAI 调用（与原脚本相同） ────────────────────────────────────────
def _parse_wait(e) -> int:
    hdr = getattr(e, "response", None)
    hdr = hdr.headers if hdr else {}
    if "retry-after" in hdr:
        try:
            return int(float(hdr["retry-after"]))
        except ValueError:
            pass
    if "x-ratelimit-reset-requests" in hdr and hdr["x-ratelimit-reset-requests"].rstrip("s").isdigit():
        return int(float(hdr["x-ratelimit-reset-requests"].rstrip("s")))
    m = re.search(r"try again in (\d+)", str(e).lower())
    return int(m.group(1)) if m else 20


def call_openai(msgs, max_retry=MAX_RETRY_SEG):
    global api_keys
    cooldown = {}
    for _ in range(max_retry):
        now = time.time()
        avail = [k for k in api_keys if cooldown.get(k, 0) <= now]
        if not avail:
            wait = max(1, min(cooldown.values()) - now)
            log.info(f"🕒 全 key 冷却，等待 {wait:.1f}s")
            time.sleep(wait);
            continue
        key = random.choice(avail)
        client.api_key = key
        try:
            rsp = client.chat.completions.create(
                model=model, messages=msgs,
                temperature=temperature, max_tokens=max_tokens)
            return rsp.choices[0].message.content.strip()
        except openai.AuthenticationError:
            log.error(f"🚫 鉴权失败({key[:8]}…) → 移除")
            api_keys.remove(key)
        except openai.RateLimitError as e:
            wait = _parse_wait(e)
            cooldown[key] = now + wait
            log.warning(f"⏳ 速率限制 {key[:8]} 冷却 {wait}s … 换下一把")
        except UnicodeEncodeError as e:
            log.error(f"🛑 UnicodeEncodeError: {e}")
            return "BAD_UNICODE"
        except Exception as e:
            log.warning(f"⚠️  其它错误({key[:8]}): {e}，3s后再试")
            time.sleep(3)
    return "API请求失败"


# ─── 提取 Word 文本 ──────────────────────────────────────────────────────
def text_from_doc(path: Path) -> str:
    """
    尝试用 textract 读取旧 .doc 文件。
    · 若没安装 textract（或其依赖 antiword、catdoc…），直接返回空串并记录日志；
    · 若读取失败，同样返回空串，避免抛异常让主程序退出。
    """
    if textract is None:
        log.error("❌ 未安装 textract，无法解析 .doc；跳过")
        return ""
    try:
        raw = textract.process(str(path))  # bytes
        return raw.decode("utf-8", errors="replace")
    except Exception as e:
        log.error(f"❌ textract 解析失败({e})")
        return ""


def text_from_docx(path: Path) -> str:
    """
    • 首选 python-docx 把段落逐行读出来；
    • 如果文件结构损坏导致 python-docx 抛异常，则回退到 docx2txt；
    • 两种方法都失败时返回空串，让上层逻辑决定跳过。
    """
    try:
        doc = docx.Document(path)
        return "\n".join(p.text for p in doc.paragraphs)
    except Exception as e:
        log.error(f"❌ python-docx 解析失败: {e}")

        # 回退到 docx2txt（需先 pip install docx2txt）
        try:
            import docx2txt
            return docx2txt.process(str(path))
        except Exception as e2:
            log.error(f"⚠️  docx2txt 也失败: {e2}")
            return ""  # 返回空串，避免抛异常


def word_text(word_path: Path) -> str:
    try:
        if word_path.suffix.lower() == ".docx":
            return scrub(text_from_docx(word_path))
        elif word_path.suffix.lower() == ".doc":
            return scrub(text_from_doc(word_path))
    except Exception as e:
        # 理论上不会走到这里，仍做二次保险
        log.error(f"❌ 解析 Word 失败({e})")
    return ""


# ─── 辅助 I/O ───────────────────────────────────────────────────────────
def safe(fname): return "".join(c for c in fname if c.isalnum() or c in "._-")


def out_path(rel): return out_dir / f"{safe(rel)}.txt"


# ─── 处理单文件 ──────────────────────────────────────────────────────────
def process_one(word: Path, rel: str):
    if out_path(rel).exists():
        log.info(f"🔹 已完成，跳过: {rel}");
        return
    log.info(f"\n▶ 处理: {rel}")

    try:
        text = word_text(word)
    except Exception as e:
        log.error(f"❌ 读取 Word 时异常({e})，跳过文件");
        return

    if not text.strip():
        log.error("❌ 无可用文本，跳过");
        return

    # —— 分段提要 —— #
    parts = []
    for i, raw in enumerate(splitter(text)):
        chunk = scrub(raw)
        log.info(f"  • 段{i + 1}, tok≈{ntokens(chunk)}")
        ans = call_openai([
            {"role": "system", "content": "你是专业报告分析助手。"},
            {"role": "user", "content": f"请用不超过120字概括下列文字核心要点：\n\n{chunk}"}
        ])
        if ans in ("API请求失败", "BAD_UNICODE"):
            log.error("❌ 分段失败，跳过文件");
            return
        parts.append(ans)
        time.sleep(BASE_SLEEP)

    joined = "\n".join(f"段{n + 1}:{s}" for n, s in enumerate(parts))
    final = call_openai([
        {"role": "system", "content": "你是专业报告分析助手。"},
        {"role": "user", "content": f"{prompt_root}\n\n以下是分段要点，请综合：\n{joined}"}
    ])
    if final in ("API请求失败", "BAD_UNICODE"):
        log.error("❌ 汇总失败，跳过文件");
        return

    try:
        js = json.loads(final)
        kw = ", ".join(js.get("keywords", [])) or "（空）"
        sm = js.get("summary", "") or "（空）"
    except Exception:
        kw, sm = "（解析失败）", final.strip()

    out_path(rel).write_text(f"文件名: {rel}\n关键词: {kw}\n摘要: {sm}\n", "utf-8")
    log.info(f"✓ 写入 {out_path(rel)}")


# ─── 主程序 ──────────────────────────────────────────────────────────────
def main():
    log.info("📄 启动 Word 摘要提取")
    for root, _, files in os.walk(docs_dir):
        for fn in files:
            if fn.lower().endswith((".docx", ".doc")):
                p = Path(root) / fn
                rel = os.path.relpath(p, docs_dir)
                process_one(p, rel)
    log.info("✅ 全部完成")


if __name__ == "__main__":
    main()
